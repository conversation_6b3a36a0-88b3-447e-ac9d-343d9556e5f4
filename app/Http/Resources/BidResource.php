<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BidResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'job_booking_id' => $this->job_booking_id,
            'provider_id' => $this->provider_id,
            'amount' => $this->amount,
            'description' => $this->description,
            'status' => $this->status,
            'estimated_completion_time' => $this->estimated_completion_time?->format('Y-m-d H:i:s'),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),

            // Job booking information (replaces service_request)
            'job_booking' => $this->whenLoaded('jobBooking', function () {
                return [
                    'id' => $this->jobBooking->id,
                    'job_uuid' => $this->jobBooking->job_uuid,
                    'project_code' => $this->jobBooking->project_code,
                    'service_category' => $this->jobBooking->service_category,
                    'description' => $this->jobBooking->description,
                    'status' => $this->jobBooking->status,
                    'schedule_date' => $this->jobBooking->schedule_date?->format('Y-m-d'),
                    'address' => $this->jobBooking->address,
                    'city' => $this->jobBooking->city,
                    'state' => $this->jobBooking->state,
                ];
            }),

            // Customer information
            'customer' => $this->when($this->relationLoaded('jobBooking') && $this->jobBooking->relationLoaded('user'), function () {
                return [
                    'id' => $this->jobBooking->user->id,
                    'name' => $this->jobBooking->user->name,
                    'email' => $this->jobBooking->user->email,
                    'phone' => $this->jobBooking->user->phone,
                ];
            }),

            // Provider information
            'provider' => $this->whenLoaded('provider', function () {
                return [
                    'id' => $this->provider->id,
                    'name' => $this->provider->name,
                    'email' => $this->provider->email,
                    'phone' => $this->provider->phone,
                ];
            }),

            // Backward compatibility - keep service_request_id as null for old clients
            'service_request_id' => null,
            'service_request' => null,
        ];
    }
}