<?php

namespace App\Repositories\API;

use App\Enums\BidStatusEnum;
use App\Enums\RoleEnum;
use App\Enums\ServiceRequestEnum;
use App\Enums\ServiceTypeEnum;
use App\Events\CreateBidEvent;
use App\Events\UpdateBidEvent;
use App\Exceptions\ExceptionHandler;
use App\Helpers\Helpers;
use App\Models\Bid;
use App\Models\Service;
use App\Models\ServiceRequest;
use Exception;
use Illuminate\Support\Facades\DB;
use Prettus\Repository\Eloquent\BaseRepository;
use Symfony\Component\HttpFoundation\Response;
use Prettus\Repository\Criteria\RequestCriteria;


class BidRepository extends BaseRepository
{
    protected $serviceRequest;

    protected $service;

    public function model()
    {
        $this->serviceRequest = new ServiceRequest();
        $this->service = new Service();
        return Bid::class;
    }

    public function  boot()
    {
        try {

            $this->pushCriteria(app(RequestCriteria::class));
        } catch (Exception $e) {

            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function store($request)
    {
        DB::beginTransaction();
        try {
            if (Helpers::getCurrentRoleName() !== RoleEnum::PROVIDER) {
                throw new Exception('errors.only_providers_can_place_bids',  Response::HTTP_BAD_REQUEST);
            }

            $provider_id = $request->provider_id ?? Helpers::getCurrentUserId();
           
            if (!$this->isExistsBidAtTime($provider_id, $request->service_request_id)) {
                $bid = $this->model->create([
                    'service_request_id' => $request->service_request_id,
                    'amount' => $request->amount,
                    'provider_id' => $provider_id,
                ]);
                event(new CreateBidEvent($bid));
                DB::commit();
                return $bid;
            }
            throw new Exception('You cannot create a new bid until the previous bid has been rejected.', 400);
        } catch (Exception $e) {
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    public function isExistsBidAtTime($provider_id, $job_booking_id)
    {
      return $this->model->whereNull('deleted_at')
          ?->where('provider_id',$provider_id)
          ?->where('job_booking_id',$job_booking_id)
          ?->where('status',BidStatusEnum::REQUESTED)?->exists();
    }

    public function update($request, $id)
    {
        DB::beginTransaction();
        try {

            $bid = $this->model->findOrFail($id);
            if ($bid->status === BidStatusEnum::REQUESTED) {
                $bid->update(['status' => $request['status']]);

                // Handle job booking status updates (replaces service request logic)
                if($bid->status == BidStatusEnum::REJECTED){
                    // For job bookings, we don't need to update status like service requests
                    // The job booking remains open for other bids
                }

                if ($bid->status == BidStatusEnum::ACCEPTED) {
                    // Use the job booking's acceptBid method instead of creating service
                    $jobBooking = $bid->jobBooking;
                    if ($jobBooking) {
                        $success = $jobBooking->acceptBid($bid->id);
                        if ($success) {
                            DB::commit();
                            return $bid->fresh();
                        } else {
                            throw new Exception('Failed to accept bid through job booking', 500);
                        }
                    }
                }

                event(new UpdateBidEvent($bid));
                DB::commit();
                return $bid->fresh();
            }

            throw new Exception("The bid status is already {$bid?->status}, you can't change again.", 403);
        } catch (Exception $e) {
            DB::rollBack();
            throw new ExceptionHandler($e->getMessage(), $e->getCode());
        }
    }

    /**
     * @deprecated This method is deprecated as the system has migrated to job bookings.
     * Job acceptance now creates Job records instead of Service records.
     * Use JobBooking::acceptBid() method instead.
     */
    public function createService($service_request_id, $bid)
    {
        throw new Exception('This method is deprecated. The system has migrated from service requests to job bookings. Use JobBooking::acceptBid() method instead.', 410);
    }
}
